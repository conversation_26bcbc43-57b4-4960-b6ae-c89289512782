namespace GraphQLApi.Services
{
    public class TrainingStatusBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TrainingStatusBackgroundService> _logger;
        private readonly TimeSpan _period = TimeSpan.FromHours(1); // Run every hour

        public TrainingStatusBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<TrainingStatusBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var trainingStatusService = scope.ServiceProvider.GetRequiredService<ITrainingStatusService>();

                    _logger.LogInformation("Starting automatic training status update");

                    await trainingStatusService.UpdateTrainingStatusesAsync();
                    await trainingStatusService.UpdateTrainingHistoryStatusesAsync();

                    _logger.LogInformation("Completed automatic training status update");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during automatic training status update");
                }

                await Task.Delay(_period, stoppingToken);
            }
        }
    }
}
