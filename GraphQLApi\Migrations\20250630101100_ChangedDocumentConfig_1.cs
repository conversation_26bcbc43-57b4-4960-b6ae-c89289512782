﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class ChangedDocumentConfig_1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "WorkerId",
                table: "Workers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TrainingId",
                table: "Trainings",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Workers_WorkerId",
                table: "Workers",
                column: "WorkerId");

            migrationBuilder.CreateIndex(
                name: "IX_Trainings_TrainingId",
                table: "Trainings",
                column: "TrainingId");

            migrationBuilder.AddForeignKey(
                name: "FK_Trainings_DocumentFiles_TrainingId",
                table: "Trainings",
                column: "TrainingId",
                principalTable: "DocumentFiles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Workers_DocumentFiles_WorkerId",
                table: "Workers",
                column: "WorkerId",
                principalTable: "DocumentFiles",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Trainings_DocumentFiles_TrainingId",
                table: "Trainings");

            migrationBuilder.DropForeignKey(
                name: "FK_Workers_DocumentFiles_WorkerId",
                table: "Workers");

            migrationBuilder.DropIndex(
                name: "IX_Workers_WorkerId",
                table: "Workers");

            migrationBuilder.DropIndex(
                name: "IX_Trainings_TrainingId",
                table: "Trainings");

            migrationBuilder.DropColumn(
                name: "WorkerId",
                table: "Workers");

            migrationBuilder.DropColumn(
                name: "TrainingId",
                table: "Trainings");
        }
    }
}
