using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public interface IJobService
    {
        // Create job
        Task<Job> CreateJobAsync(
            string title,
            IEnumerable<int> workerIds,
            int chiefEngineerId,
            string timeForCompletion,
            DateTime? startDate = null,
            string? description = null);

        // Get jobs by status
        Task<IEnumerable<Job>> GetRequestedJobsAsync();
        Task<IEnumerable<Job>> GetBlockedJobsAsync();
        Task<IEnumerable<Job>> GetPendingApprovalJobsAsync();
        Task<IEnumerable<Job>> GetApprovedJobsAsync();
        Task<IEnumerable<Job>> GetDisapprovedJobsAsync();
        Task<IEnumerable<Job>> GetFinishedJobsAsync();

        // Job operations
        Task<Job> BlockJobAsync(int jobId, int blockedById);
        Task<Job> ReviewJobAsync(
            int jobId,
            int reviewedById,
            IEnumerable<HazardInput> hazards,
            IEnumerable<ServiceDocumentFileInput> documents,
            IEnumerable<PermitType> requiredPermits);
        Task<Job> ApproveJobAsync(int jobId, int approvedById);
        Task<Job> DisapproveJobAsync(int jobId, int disapprovedById);
        Task<Job> FinishJobAsync(int jobId, int finishedById);

        // General queries
        Task<Job?> GetJobByIdAsync(int id);
        Task<IEnumerable<Job>> GetAllJobsAsync();
        Task<IEnumerable<Job>> GetJobsByStatusAsync(JobStatus status);
        Task<IEnumerable<Job>> GetJobsByWorkerIdAsync(int workerId);
        Task<IEnumerable<Job>> GetJobsByChiefEngineerIdAsync(int chiefEngineerId);
    }

    // Input classes for complex operations
    public class HazardInput
    {
        public string Description { get; set; } = string.Empty;
        public IEnumerable<string> ControlMeasures { get; set; } = new List<string>();
    }

    public class ServiceDocumentFileInput
    {
        public string Name { get; set; } = string.Empty;
        public int FileMetadataId { get; set; }
    }
}
