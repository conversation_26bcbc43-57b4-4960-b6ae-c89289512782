using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.Services
{
    public class WorkerService : IWorkerService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IPhotoService _photoService;
        private readonly ILogger<WorkerService> _logger;

        public WorkerService(
            IDbContextFactory<AppDbContext> contextFactory,
            IPhotoService photoService,
            ILogger<WorkerService> logger)
        {
            _contextFactory = contextFactory;
            _photoService = photoService;
            _logger = logger;
        }



        public async Task<IEnumerable<Worker>> GetAllWorkersAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Workers.ToListAsync();
        }

        public async Task<Worker?> GetWorkerByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Workers.FirstOrDefaultAsync(w => w.Id == id);
        }

        public async Task<Worker> CreateWorkerAsync(Worker worker)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Check if worker with same National ID already exists
            var existingWorker = await context.Workers
                .FirstOrDefaultAsync(w => w.NationalId == worker.NationalId);

            if (existingWorker != null)
            {
                throw new GraphQLException(new Error(
                    "Validation",
                    $"A worker with National ID '{worker.NationalId}' already exists.")
                );
            }

            context.Workers.Add(worker);
            await context.SaveChangesAsync();
            return worker;
        }

        public async Task<Worker?> UpdateWorkerAsync(int id, Worker updatedWorker)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var worker = await context.Workers.FirstOrDefaultAsync(w => w.Id == id);

            if (worker == null)
                return null;

            // Update properties
            worker.Name = updatedWorker.Name;
            worker.Company = updatedWorker.Company;
            worker.DateOfBirth = updatedWorker.DateOfBirth;
            worker.ManHours = updatedWorker.ManHours;
            worker.Rating = updatedWorker.Rating;
            worker.Gender = updatedWorker.Gender;
            worker.PhoneNumber = updatedWorker.PhoneNumber;
            worker.Email = updatedWorker.Email;
            worker.InductionDate = updatedWorker.InductionDate;
            worker.MedicalCheckDate = updatedWorker.MedicalCheckDate;
            worker.MpesaNumber = updatedWorker.MpesaNumber;

            await context.SaveChangesAsync();
            return worker;
        }

        public async Task<bool> DeleteWorkerAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var worker = await context.Workers.FindAsync(id);
            if (worker == null)
                return false;

            try
            {
                // Note: File cleanup for profile pictures and signatures is handled by the MinIO service
                // when the worker is deleted, the associated file metadata will be cleaned up separately

                context.Workers.Remove(worker);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting worker with ID {WorkerId}", id);
                throw;
            }
        }

        // Note: Photo upload functionality has been replaced by the file upload system
        // in the GraphQL mutations. Profile pictures are now handled through the
        // ProfilePictureFile relationship and MinIO storage.
    }
}