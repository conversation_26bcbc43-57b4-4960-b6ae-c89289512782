using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Data.Configurations
{
    /// <summary>
    /// Entity configuration for FileMetadata
    /// </summary>
    public class FileMetadataConfiguration : IEntityTypeConfiguration<FileMetadata>
    {
        public void Configure(EntityTypeBuilder<FileMetadata> builder)
        {
            builder.ToTable("FileMetadata");

            // Primary key
            builder.HasKey(f => f.Id);
            builder.Property(f => f.Id)
                .UseIdentityColumn();

            // Required properties
            builder.Property(f => f.FileName)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(f => f.ContentType)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(f => f.Size)
                .IsRequired();

            builder.Property(f => f.<PERSON>)
                .IsRequired()
                .HasMaxLength(63); // MinIO bucket name limit

            builder.Property(f => f.<PERSON><PERSON>)
                .IsRequired()
                .HasMaxLength(1024); // MinIO object key limit

            // Optional properties
            builder.Property(f => f.Description)
                .HasMaxLength(1000);

            builder.Property(f => f.ETag)
                .HasMaxLength(100);

            builder.Property(f => f.Version)
                .HasMaxLength(100);

            builder.Property(f => f.FolderPath)
                .HasMaxLength(500);

            builder.Property(f => f.AdditionalMetadata)
                .HasColumnType("nvarchar(max)"); // JSON storage

            // Enum conversion
            builder.Property(f => f.FileType)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            // Boolean properties
            builder.Property(f => f.IsPublic)
                .IsRequired()
                .HasDefaultValue(false);

            // Audit fields
            builder.Property(f => f.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(f => f.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(f => f.UpdatedAt)
                .HasColumnType("datetime2");

            builder.Property(f => f.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(f => f.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(f => f.DeletedAt)
                .HasColumnType("datetime2");

            builder.Property(f => f.DeletedBy)
                .HasMaxLength(100);

            builder.Property(f => f.ExpiresAt)
                .HasColumnType("datetime2");

            // Indexes for performance
            builder.HasIndex(f => f.BucketName)
                .HasDatabaseName("IX_FileMetadata_BucketName");

            builder.HasIndex(f => f.ObjectKey)
                .HasDatabaseName("IX_FileMetadata_ObjectKey");

            builder.HasIndex(f => new { f.BucketName, f.ObjectKey })
                .IsUnique()
                .HasFilter("[IsDeleted] = 0")
                .HasDatabaseName("IX_FileMetadata_BucketName_ObjectKey_Unique");

            builder.HasIndex(f => f.FileType)
                .HasDatabaseName("IX_FileMetadata_FileType");

            builder.HasIndex(f => f.CreatedAt)
                .HasDatabaseName("IX_FileMetadata_CreatedAt");

            builder.HasIndex(f => f.ExpiresAt)
                .HasFilter("[ExpiresAt] IS NOT NULL")
                .HasDatabaseName("IX_FileMetadata_ExpiresAt");

            builder.HasIndex(f => f.IsDeleted)
                .HasDatabaseName("IX_FileMetadata_IsDeleted");

            builder.HasIndex(f => f.FolderPath)
                .HasFilter("[FolderPath] IS NOT NULL")
                .HasDatabaseName("IX_FileMetadata_FolderPath");

            // Relationships (if needed in the future)
            // These are commented out as they may not be needed initially
            /*
            builder.HasMany(f => f.WorkersWithProfilePicture)
                .WithOne()
                .HasForeignKey("ProfilePictureFileId")
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasMany(f => f.ToolboxSessions)
                .WithOne()
                .HasForeignKey("PhotoFileId")
                .OnDelete(DeleteBehavior.SetNull);
            */
        }
    }
}
