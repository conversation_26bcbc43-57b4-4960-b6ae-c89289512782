﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class Task : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Description { get; set; } = "";
        public Shared.Enums.TaskStatus Status { get; set; } = Shared.Enums.TaskStatus.TODO;
        public string TaskNumber { get; set; } // Format: TSK-YEAR-number
        public Shared.Enums.TaskPriority Priority { get; set; } = Shared.Enums.TaskPriority.MEDIUM;
        public string? TimeForCompletion { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public string? Category { get; set; }
        public Shared.Enums.InspectionStatus InspectionStatus { get; set; } = Shared.Enums.InspectionStatus.NOT_REQUIRED;
        public string? AssociatedMethodStatement { get; set; }

        // Navigation Properties
        public int? ChiefEngineerId { get; set; }
        public Worker? ChiefEngineer { get; set; }

        public ICollection<Worker> WorkersAssigned { get; set; } = new List<Worker>();
        public ICollection<Equipment> EquipmentInvolved { get; set; } = new List<Equipment>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
