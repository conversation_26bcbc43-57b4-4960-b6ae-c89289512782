namespace Shared.Configuration
{
    /// <summary>
    /// Configuration model for MinIO settings
    /// </summary>
    public class MinIOConfiguration
    {
        public const string SectionName = "MinIO";

        /// <summary>
        /// MinIO server endpoint (e.g., localhost:9000)
        /// </summary>
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// MinIO access key
        /// </summary>
        public string AccessKey { get; set; } = string.Empty;

        /// <summary>
        /// MinIO secret key
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// Whether to use SSL/TLS
        /// </summary>
        public bool UseSSL { get; set; } = false;

        /// <summary>
        /// MinIO region (optional)
        /// </summary>
        public string Region { get; set; } = "us-east-1";

        /// <summary>
        /// Bucket name mappings
        /// </summary>
        public BucketConfiguration Buckets { get; set; } = new();

        /// <summary>
        /// Additional MinIO settings
        /// </summary>
        public MinIOSettings Settings { get; set; } = new();
    }

    /// <summary>
    /// Bucket configuration mapping
    /// </summary>
    public class BucketConfiguration
    {
        public string ProfilePicture { get; set; } = "profile-picture";
        public string Certification { get; set; } = "certification";
        public string Signatures { get; set; } = "signatures";
        public string Temp { get; set; } = "temp";
        public string Docs { get; set; } = "docs";

        /// <summary>
        /// Get all bucket names as a list
        /// </summary>
        public List<string> GetAllBuckets()
        {
            return new List<string>
            {
                ProfilePicture,
                Certification,
                Signatures,
                Temp,
                Docs
            };
        }
    }

    /// <summary>
    /// Additional MinIO settings
    /// </summary>
    public class MinIOSettings
    {
        /// <summary>
        /// Maximum file size in bytes (default: 50MB)
        /// </summary>
        public long MaxFileSize { get; set; } = 50 * 1024 * 1024;

        /// <summary>
        /// Default expiration for temporary files in days
        /// </summary>
        public int DefaultExpiration { get; set; } = 7;

        /// <summary>
        /// Whether to enable versioning on buckets
        /// </summary>
        public bool EnableVersioning { get; set; } = true;

        /// <summary>
        /// Whether to automatically create buckets at startup
        /// </summary>
        public bool AutoCreateBuckets { get; set; } = true;

        /// <summary>
        /// Connection timeout in seconds
        /// </summary>
        public int ConnectionTimeout { get; set; } = 30;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int RequestTimeout { get; set; } = 300;
    }
}
