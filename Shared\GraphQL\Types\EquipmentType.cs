using HotChocolate.Types;
using Shared.GraphQL.Models;
using HotChocolate;

namespace Shared.GraphQL.Types
{
    public class EquipmentType : ObjectType<Equipment>
    {
        protected override void Configure(IObjectTypeDescriptor<Equipment> descriptor)
        {
            descriptor.Field(x => x.Id).Type<NonNullType<IntType>>();
            descriptor.Field(x => x.Name).Type<NonNullType<StringType>>();
            descriptor.Field(x => x.Description).Type<StringType>();
            descriptor.Field(x => x.SerialNumber).Type<StringType>();
            descriptor.Field(x => x.Model).Type<StringType>();
            descriptor.Field(x => x.Manufacturer).Type<StringType>();
            descriptor.Field(x => x.PurchaseDate).Type<DateTimeType>();
            descriptor.Field(x => x.LastMaintenanceDate).Type<DateTimeType>();
            descriptor.Field(x => x.NextMaintenanceDate).Type<DateTimeType>();
            descriptor.Field(x => x.Location).Type<StringType>();
            descriptor.Field(x => x.Status).Type<StringType>();
            descriptor.Field(x => x.PurchasePrice).Type<DecimalType>();
            descriptor.Field(x => x.Category).Type<StringType>();
            descriptor.Field(x => x.Tasks).Type<ListType<TaskType>>();
            descriptor.Field(x => x.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(x => x.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(x => x.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(x => x.UpdatedBy).Type<StringType>();
        }
    }
}
