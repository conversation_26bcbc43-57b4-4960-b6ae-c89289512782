using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class ExcavationWorkPermitConfiguration : IEntityTypeConfiguration<ExcavationWorkPermit>
    {
        public void Configure(EntityTypeBuilder<ExcavationWorkPermit> builder)
        {
            builder.Property(p => p.DepthOfExcavation)
                .HasMaxLength(200);

            builder.Property(p => p.ProtectionSystems)
                .HasMaxLength(1000);

            builder.Property(p => p.ListOfEquipmentToBeUsed)
                .HasMaxLength(1000);

            builder.Property(p => p.Inspections)
                .HasMaxLength(1000);

            builder.Property(p => p.InspectionAuthorization)
                .IsRequired()
                .HasColumnType("nvarchar(max)");
        }
    }
}
