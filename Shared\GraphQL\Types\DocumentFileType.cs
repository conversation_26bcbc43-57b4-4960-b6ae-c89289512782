using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    /// <summary>
    /// GraphQL type for DocumentFile entity
    /// </summary>
    public class DocumentFileType : ObjectType<DocumentFile>
    {
        protected override void Configure(IObjectTypeDescriptor<DocumentFile> descriptor)
        {
            descriptor.Description("Represents a document file with a custom name");

            descriptor.Field(f => f.Id)
                .Description("Unique identifier for the document file");

            descriptor.Field(f => f.Name)
                .Description("Custom name/title for the document");

            descriptor.Field(f => f.FileMetadataId)
                .Description("Foreign key to the file metadata");

            descriptor.Field(f => f.FileMetadata)
                .Description("The underlying file metadata");

            descriptor.Field(f => f.Url)
                .Description("URL to access the file");

            // Audit fields
            descriptor.Field(f => f.CreatedAt)
                .Description("When the document file was created");

            descriptor.Field(f => f.CreatedBy)
                .Description("Who created the document file");

            descriptor.Field(f => f.UpdatedAt)
                .Description("When the document file was last updated");

            descriptor.Field(f => f.UpdatedBy)
                .Description("Who last updated the document file");

            // Hide soft delete fields from GraphQL
            descriptor.Field(f => f.IsDeleted).Ignore();
            descriptor.Field(f => f.DeletedAt).Ignore();
            descriptor.Field(f => f.DeletedBy).Ignore();
        }
    }
}
