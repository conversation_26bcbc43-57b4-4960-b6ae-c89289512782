using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class ConfinedSpacePermitConfiguration : IEntityTypeConfiguration<ConfinedSpacePermit>
    {
        public void Configure(EntityTypeBuilder<ConfinedSpacePermit> builder)
        {
            builder.Property(p => p.WorkersHaveBeenTrained)
                .IsRequired();

            builder.Property(p => p.NameOfTrainingOrganization)
                .HasMaxLength(200);

            builder.Property(p => p.TopReading)
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            builder.Property(p => p.MidReading)
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            builder.Property(p => p.BottomReading)
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            builder.Property(p => p.EmergencyGuidelines)
                .HasMaxLength(2000);

            builder.Property(p => p.TaskObserver)
                .IsRequired()
                .HasColumnType("nvarchar(max)");
        }
    }
}
