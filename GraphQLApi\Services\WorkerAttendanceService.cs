using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public class WorkerAttendanceService : IWorkerAttendanceService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IPhotoService _photoService;
        private readonly IHikvisionService _hikvisionService;
        private readonly ILogger<WorkerAttendanceService> _logger;

        public WorkerAttendanceService(
            IDbContextFactory<AppDbContext> contextFactory,
            IPhotoService photoService,
            IHikvisionService hikvisionService,
            ILogger<WorkerAttendanceService> logger)
        {
            _contextFactory = contextFactory;
            _photoService = photoService;
            _hikvisionService = hikvisionService;
            _logger = logger;
        }

        public async Task<WorkerAttendance> RecordAttendanceAsync(int workerId, DateTime timestamp, string status, string? notes = null)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var worker = await context.Workers.FindAsync(workerId);
            if (worker == null)
                throw new ArgumentException("Worker not found", nameof(workerId));

            var attendance = new WorkerAttendance
            {
                WorkerId = workerId,
                CheckInTime = timestamp,
                Status = status,
                Notes = notes,
                IsVerifiedByHikvision = false
            };

            context.WorkerAttendances.Add(attendance);
            await context.SaveChangesAsync();

            return attendance;
        }

        public async Task<WorkerAttendance?> UpdateAttendanceAsync(int attendanceId, DateTime? checkOutTime, string? notes)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var attendance = await context.WorkerAttendances.FindAsync(attendanceId);
            if (attendance == null)
                return null;

            if (checkOutTime.HasValue)
                attendance.CheckOutTime = checkOutTime;
            
            if (notes != null)
                attendance.Notes = notes;

            await context.SaveChangesAsync();
            return attendance;
        }

        public async Task<IEnumerable<WorkerAttendance>> GetWorkerAttendanceAsync(int workerId, DateTime from, DateTime to)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.WorkerAttendances
                .Where(a => a.WorkerId == workerId && a.CheckInTime >= from && a.CheckInTime <= to)
                .OrderByDescending(a => a.CheckInTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<WorkerAttendance>> GetDailyAttendanceAsync(DateTime date)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var startOfDay = date.Date;
            var endOfDay = startOfDay.AddDays(1).AddTicks(-1);

            return await context.WorkerAttendances
                .Include(a => a.Worker)
                .Where(a => a.CheckInTime >= startOfDay && a.CheckInTime <= endOfDay)
                .OrderBy(a => a.Worker.Name)
                .ToListAsync();
        }

        public async Task<ToolboxSession> CreateToolboxSessionAsync(
            string topic,
            string conductor,
            Stream groupPhoto,
            DateTime sessionTime,
            int[] workerIds,
            string? notes = null)
        {
            // Upload group photo
            var photoFileName = $"toolbox_{sessionTime:yyyyMMdd}_{Guid.NewGuid()}.jpg";
            var photoUrl = await _photoService.UploadPhotoAsync(groupPhoto, photoFileName, PhotoStorageType.Local);

            var session = new ToolboxSession
            {
                Topic = topic,
                Conductor = conductor,
                SessionTime = sessionTime,
                PhotoUrl = photoUrl,
                Notes = notes
            };

            // Create attendances for all workers
            foreach (var workerId in workerIds)
            {
                session.Attendances.Add(new ToolboxAttendance
                {
                    WorkerId = workerId,
                    WasPresent = true
                });
            }

            await using var context = await _contextFactory.CreateDbContextAsync();
            context.ToolboxSessions.Add(session);
            await context.SaveChangesAsync();

            return session;
        }

        public async Task<IEnumerable<ToolboxSession>> GetToolboxSessionsAsync(DateTime from, DateTime to)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ToolboxSessions
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Worker)
                .Where(s => s.SessionTime >= from && s.SessionTime <= to)
                .OrderByDescending(s => s.SessionTime)
                .ToListAsync();
        }

        public async Task<ToolboxSession?> GetToolboxSessionAsync(int sessionId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ToolboxSessions
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Worker)
                .FirstOrDefaultAsync(s => s.Id == sessionId);
        }

        public async Task<bool> VerifyAttendanceWithHikvisionAsync(int attendanceId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var attendance = await context.WorkerAttendances
                .Include(a => a.Worker)
                .FirstOrDefaultAsync(a => a.Id == attendanceId);

            if (attendance == null)
                throw new ArgumentException("Attendance record not found", nameof(attendanceId));

            try
            {
                //@TODO: implement the actual Hikvision verification logic
                // This is a placeholder implementation
                attendance.IsVerifiedByHikvision = true;
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying attendance with Hikvision for attendance {AttendanceId}", attendanceId);
                return false;
            }
        }
    }
} 