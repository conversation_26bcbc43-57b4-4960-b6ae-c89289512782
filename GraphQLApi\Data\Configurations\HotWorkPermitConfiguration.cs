using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class HotWorkPermitConfiguration : IEntityTypeConfiguration<HotWorkPermit>
    {
        public void Configure(EntityTypeBuilder<HotWorkPermit> builder)
        {
            builder.Property(p => p.NatureOfWork)
                .HasMaxLength(500);

            builder.Property(p => p.FireExtinguishers)
                .HasMaxLength(500);

            builder.Property(p => p.FireSafetySupervisor)
                .IsRequired()
                .HasColumnType("nvarchar(max)");
        }
    }
}
