using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public class EquipmentService : IEquipmentService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public EquipmentService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<IEnumerable<Equipment>> GetAllEquipmentAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Equipment
                .Include(e => e.Tasks)
                .ToListAsync();
        }

        public async Task<Equipment?> GetEquipmentByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Equipment
                .Include(e => e.Tasks)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<Equipment> CreateEquipmentAsync(Equipment equipment)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            context.Equipment.Add(equipment);
            await context.SaveChangesAsync();
            return equipment;
        }

        public async Task<Equipment?> UpdateEquipmentAsync(int id, Equipment equipment)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var existingEquipment = await context.Equipment.FindAsync(id);
            
            if (existingEquipment == null)
                return null;

            // Update properties
            existingEquipment.Name = equipment.Name;
            existingEquipment.Description = equipment.Description;
            existingEquipment.SerialNumber = equipment.SerialNumber;
            existingEquipment.Model = equipment.Model;
            existingEquipment.Manufacturer = equipment.Manufacturer;
            existingEquipment.PurchaseDate = equipment.PurchaseDate;
            existingEquipment.LastMaintenanceDate = equipment.LastMaintenanceDate;
            existingEquipment.NextMaintenanceDate = equipment.NextMaintenanceDate;
            existingEquipment.Location = equipment.Location;
            existingEquipment.Status = equipment.Status;
            existingEquipment.PurchasePrice = equipment.PurchasePrice;
            existingEquipment.Category = equipment.Category;

            await context.SaveChangesAsync();
            return existingEquipment;
        }

        public async Task<bool> DeleteEquipmentAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var equipment = await context.Equipment.FindAsync(id);
            
            if (equipment == null)
                return false;

            context.Equipment.Remove(equipment);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByStatusAsync(string status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Equipment
                .Include(e => e.Tasks)
                .Where(e => e.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByCategoryAsync(string category)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Equipment
                .Include(e => e.Tasks)
                .Where(e => e.Category == category)
                .ToListAsync();
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByLocationAsync(string location)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Equipment
                .Include(e => e.Tasks)
                .Where(e => e.Location == location)
                .ToListAsync();
        }
    }
}
