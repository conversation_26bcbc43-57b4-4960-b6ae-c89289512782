{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=WorkforceManagementNew;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "HikvisionApi": {"BaseUrl": "https://your-hikvision-api-url", "ApiKey": "your-api-key-here"}, "PhotoStorage": {"LocalPath": "wwwroot/photos"}, "MinIO": {"Endpoint": "localhost:9000", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "UseSSL": false, "Region": "us-east-1", "Buckets": {"ProfilePicture": "profile-picture", "Certification": "certification", "Signatures": "signatures", "Temp": "temp", "Docs": "docs"}, "Settings": {"MaxFileSize": 52428800, "DefaultExpiration": 7, "EnableVersioning": true, "AutoCreateBuckets": true}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}