using Shared.Interfaces;
using Shared.Enums;

namespace Shared.GraphQL.Models
{
    public class WorkerTrainingHistory : IAuditableEntity
    {
        public int Id { get; set; }
        public int WorkerId { get; set; }
        public Worker Worker { get; set; } = null!;
        public int TrainingId { get; set; }
        public Training Training { get; set; } = null!;
        
        public DateTime CompletionDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public TrainingStatus Status { get; set; }
        public string? Notes { get; set; }
        public string? CertificateUrl { get; set; }
        public decimal? Score { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Computed Properties
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;
        public bool IsExpiringSoon => ExpiryDate.HasValue && ExpiryDate.Value > DateTime.UtcNow && ExpiryDate.Value <= DateTime.UtcNow.AddDays(30);
        public int? DaysUntilExpiry => ExpiryDate.HasValue
            ? (ExpiryDate.Value < DateTime.UtcNow
                ? 0
                : (int)(ExpiryDate.Value - DateTime.UtcNow).TotalDays)
            : null;
    }
}
