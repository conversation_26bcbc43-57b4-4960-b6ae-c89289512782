using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class ControlMeasureType : ObjectType<ControlMeasure>
    {
        protected override void Configure(IObjectTypeDescriptor<ControlMeasure> descriptor)
        {
            descriptor.Field(cm => cm.Id).Type<NonNullType<IntType>>();
            descriptor.Field(cm => cm.Description).Type<NonNullType<StringType>>();

            // Hazard relationship
            descriptor.Field(cm => cm.HazardId).Type<NonNullType<IntType>>();
            descriptor.Field(cm => cm.Hazard).Type<HazardType>();

            // Audit fields
            descriptor.Field(cm => cm.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(cm => cm.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(cm => cm.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(cm => cm.UpdatedBy).Type<StringType>();
        }
    }
}
