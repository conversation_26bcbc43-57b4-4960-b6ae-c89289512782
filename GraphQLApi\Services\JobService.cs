using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public class JobService : IJobService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public JobService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<Job> CreateJobAsync(
            string title,
            IEnumerable<int> workerIds,
            int chiefEngineerId,
            string timeForCompletion,
            DateTime? startDate = null,
            string? description = null)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate chief engineer exists
            var chiefEngineer = await context.Workers.FindAsync(chiefEngineerId);
            if (chiefEngineer == null)
                throw new ArgumentException($"Chief engineer with ID {chiefEngineerId} not found");

            // Validate workers exist
            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();
            
            if (workers.Count != workerIds.Count())
                throw new ArgumentException("One or more worker IDs are invalid");

            var job = new Job
            {
                Title = title,
                Description = description,
                Status = JobStatus.REQUESTED,
                TimeForCompletion = timeForCompletion,
                StartDate = startDate ?? DateTime.Today.AddDays(1),
                ChiefEngineerId = chiefEngineerId,
                RequestedDate = DateTime.UtcNow,
                Workers = workers
            };

            // Calculate due date
            if (TimeSpan.TryParse(timeForCompletion, out var duration))
            {
                job.DueDate = job.StartDate.Add(duration);
            }

            context.Jobs.Add(job);
            await context.SaveChangesAsync();

            // TODO: notification added here

            return await GetJobByIdAsync(job.Id) ?? job;
        }

        public async Task<IEnumerable<Job>> GetRequestedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.REQUESTED);
        }

        public async Task<Job> BlockJobAsync(int jobId, int blockedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Workers)
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.REQUESTED)
                throw new InvalidOperationException($"Cannot block job with status {job.Status}. Only REQUESTED jobs can be blocked.");

            // Validate blocker exists
            var blocker = await context.Workers.FindAsync(blockedById);
            if (blocker == null)
                throw new ArgumentException($"Worker with ID {blockedById} not found");

            job.Status = JobStatus.BLOCKED;
            job.BlockedById = blockedById;
            job.BlockedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // TODO: notification added here

            return job;
        }

        public async Task<IEnumerable<Job>> GetBlockedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.BLOCKED);
        }

        public async Task<Job> ReviewJobAsync(
            int jobId,
            int reviewedById,
            IEnumerable<HazardInput> hazards,
            IEnumerable<ServiceDocumentFileInput> documents,
            IEnumerable<PermitType> requiredPermits)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Workers)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .Include(j => j.Documents)
                    .ThenInclude(d => d.FileMetadata)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.REQUESTED && job.Status != JobStatus.DISAPPROVED)
                throw new InvalidOperationException($"Cannot review job with status {job.Status}. Only REQUESTED or DISAPPROVED jobs can be reviewed.");

            // Validate reviewer exists
            var reviewer = await context.Workers.FindAsync(reviewedById);
            if (reviewer == null)
                throw new ArgumentException($"Worker with ID {reviewedById} not found");

            // Update job status and reviewer info
            job.Status = JobStatus.PENDING_APPROVAL;
            job.ReviewedById = reviewedById;
            job.ReviewedDate = DateTime.UtcNow;
            job.RequiredPermits = requiredPermits.ToList();

            // Clear existing hazards and add new ones
            context.Hazards.RemoveRange(job.Hazards);
            job.Hazards.Clear();

            foreach (var hazardInput in hazards)
            {
                var hazard = new Hazard
                {
                    Description = hazardInput.Description,
                    JobId = jobId
                };

                foreach (var controlMeasureDesc in hazardInput.ControlMeasures)
                {
                    hazard.ControlMeasures.Add(new ControlMeasure
                    {
                        Description = controlMeasureDesc
                    });
                }

                job.Hazards.Add(hazard);
            }

            // Handle documents - validate they exist and are in docs bucket
            var documentFileMetadataIds = documents.Select(d => d.FileMetadataId).ToList();
            var fileMetadatas = await context.FileMetadata
                .Where(fm => documentFileMetadataIds.Contains(fm.Id) && fm.BucketName == "docs")
                .ToListAsync();

            if (fileMetadatas.Count != documentFileMetadataIds.Count)
                throw new ArgumentException("One or more document file metadata IDs are invalid or not in docs bucket");

            // Clear existing documents and add new ones
            job.Documents.Clear();
            foreach (var docInput in documents)
            {
                var documentFile = new DocumentFile
                {
                    Name = docInput.Name,
                    FileMetadataId = docInput.FileMetadataId
                };
                context.DocumentFiles.Add(documentFile);
                job.Documents.Add(documentFile);
            }

            await context.SaveChangesAsync();

            return job;
        }

        public async Task<IEnumerable<Job>> GetPendingApprovalJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.PENDING_APPROVAL);
        }

        public async Task<Job> ApproveJobAsync(int jobId, int approvedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Workers)
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.PENDING_APPROVAL)
                throw new InvalidOperationException($"Cannot approve job with status {job.Status}. Only PENDING_APPROVAL jobs can be approved.");

            // Validate approver exists
            var approver = await context.Workers.FindAsync(approvedById);
            if (approver == null)
                throw new ArgumentException($"Worker with ID {approvedById} not found");

            job.Status = JobStatus.APPROVED;
            job.ApprovedById = approvedById;
            job.ApprovedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // TODO: notification added here

            return job;
        }

        public async Task<Job> DisapproveJobAsync(int jobId, int disapprovedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Workers)
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.PENDING_APPROVAL)
                throw new InvalidOperationException($"Cannot disapprove job with status {job.Status}. Only PENDING_APPROVAL jobs can be disapproved.");

            // Validate disapprover exists
            var disapprover = await context.Workers.FindAsync(disapprovedById);
            if (disapprover == null)
                throw new ArgumentException($"Worker with ID {disapprovedById} not found");

            job.Status = JobStatus.DISAPPROVED;
            job.ApprovedById = disapprovedById; // Using ApprovedBy field for disapprover as specified
            job.ApprovedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // TODO: notification added here

            return job;
        }

        public async Task<IEnumerable<Job>> GetDisapprovedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.DISAPPROVED);
        }

        public async Task<IEnumerable<Job>> GetApprovedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.APPROVED);
        }

        public async Task<Job> FinishJobAsync(int jobId, int finishedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Workers)
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.APPROVED)
                throw new InvalidOperationException($"Cannot finish job with status {job.Status}. Only APPROVED jobs can be finished.");

            // Validate finisher exists
            var finisher = await context.Workers.FindAsync(finishedById);
            if (finisher == null)
                throw new ArgumentException($"Worker with ID {finishedById} not found");

            job.Status = JobStatus.FINISHED;
            job.FinishedById = finishedById;
            job.FinishedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            return job;
        }

        public async Task<IEnumerable<Job>> GetFinishedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.FINISHED);
        }

        public async Task<Job?> GetJobByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.RequestedBy)
                .Include(j => j.BlockedBy)
                .Include(j => j.ReviewedBy)
                .Include(j => j.ApprovedBy)
                .Include(j => j.FinishedBy)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Workers)
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .Include(j => j.Documents)
                    .ThenInclude(d => d.FileMetadata)
                .FirstOrDefaultAsync(j => j.Id == id);
        }

        public async Task<IEnumerable<Job>> GetAllJobsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Workers)
                .ToListAsync();
        }

        public async Task<IEnumerable<Job>> GetJobsByStatusAsync(JobStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Workers)
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .Include(j => j.Documents)
                    .ThenInclude(d => d.FileMetadata)
                .Where(j => j.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Job>> GetJobsByWorkerIdAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Workers)
                .Where(j => j.Workers.Any(w => w.Id == workerId))
                .ToListAsync();
        }

        public async Task<IEnumerable<Job>> GetJobsByChiefEngineerIdAsync(int chiefEngineerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Workers)
                .Where(j => j.ChiefEngineerId == chiefEngineerId)
                .ToListAsync();
        }
    }
}
