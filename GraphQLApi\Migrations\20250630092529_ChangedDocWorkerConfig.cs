﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class ChangedDocWorkerConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentFiles_Workers_WorkerId",
                table: "DocumentFiles");

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentFiles_Workers_WorkerId",
                table: "DocumentFiles",
                column: "WorkerId",
                principalTable: "Workers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentFiles_Workers_WorkerId",
                table: "DocumentFiles");

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentFiles_Workers_WorkerId",
                table: "DocumentFiles",
                column: "WorkerId",
                principalTable: "Workers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
