using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IWorkerService
    {
        Task<IEnumerable<Worker>> GetAllWorkersAsync();
        Task<Worker?> GetWorkerByIdAsync(int id);
        Task<Worker> CreateWorkerAsync(Worker worker);
        Task<Worker?> UpdateWorkerAsync(int id, Worker worker);
        Task<bool> DeleteWorkerAsync(int id);
        //Task<string> UploadWorkerPhotoAsync(int workerId, Stream photoStream);
    }
} 