using Microsoft.EntityFrameworkCore;

namespace GraphQLApi.Data
{
    public static class DatabaseInitializer
    {
        public static async Task InitializeDatabaseAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var contextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<AppDbContext>>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
            
            try
            {
                await using var dbContext = await contextFactory.CreateDbContextAsync();
                if (dbContext.Database.IsSqlServer())
                {
                    await dbContext.Database.MigrateAsync();
                    logger.LogInformation("Database migrated successfully");
                }
                else
                {
                    await dbContext.Database.EnsureCreatedAsync();
                    logger.LogInformation("Database created successfully");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while initializing the database.");
                throw;
            }
        }
    }
} 