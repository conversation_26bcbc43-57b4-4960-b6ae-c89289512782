namespace GraphQLApi.Services
{
    /// <summary>
    /// Service for initializing MinIO buckets at application startup
    /// </summary>
    public interface IBucketInitializationService
    {
        /// <summary>
        /// Initialize all required buckets for the application
        /// </summary>
        /// <returns>True if all buckets were initialized successfully</returns>
        Task<bool> InitializeAsync();
    }
}
