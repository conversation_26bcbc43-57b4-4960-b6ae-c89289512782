﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkerManagement", "WorkerManagement\WorkerManagement.csproj", "{7D2F60B3-051E-4EF8-BE8F-E8DE6A1B9F6C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthSafetyEnvironment", "HealthSafetyEnvironment\HealthSafetyEnvironment.csproj", "{BF997111-F51B-4497-886A-DFD204686340}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PayrollManagement", "PayrollManagement\PayrollManagement.csproj", "{CEA8E73A-7ECA-4A6A-AA11-FE7428DA4FD8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Reporting", "Reporting\Reporting.csproj", "{0B09DE8E-9B7B-477F-86DF-71A4F88F5080}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditManagement", "AuditManagement\AuditManagement.csproj", "{B45E393C-C722-4AF1-B337-7E207D6F54E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TasksManagement", "TasksManagement\TasksManagement.csproj", "{0C2130CC-7FDA-4EA3-B554-A0F9AF5FD719}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GraphQLApi", "GraphQLApi\GraphQLApi.csproj", "{A427250D-A012-46FE-B105-D9365FFD8321}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shared", "Shared\Shared.csproj", "{42DB5BFA-D441-4518-9654-D405119FCE8D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7D2F60B3-051E-4EF8-BE8F-E8DE6A1B9F6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D2F60B3-051E-4EF8-BE8F-E8DE6A1B9F6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D2F60B3-051E-4EF8-BE8F-E8DE6A1B9F6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D2F60B3-051E-4EF8-BE8F-E8DE6A1B9F6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF997111-F51B-4497-886A-DFD204686340}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF997111-F51B-4497-886A-DFD204686340}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF997111-F51B-4497-886A-DFD204686340}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF997111-F51B-4497-886A-DFD204686340}.Release|Any CPU.Build.0 = Release|Any CPU
		{CEA8E73A-7ECA-4A6A-AA11-FE7428DA4FD8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEA8E73A-7ECA-4A6A-AA11-FE7428DA4FD8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEA8E73A-7ECA-4A6A-AA11-FE7428DA4FD8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEA8E73A-7ECA-4A6A-AA11-FE7428DA4FD8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B09DE8E-9B7B-477F-86DF-71A4F88F5080}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B09DE8E-9B7B-477F-86DF-71A4F88F5080}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B09DE8E-9B7B-477F-86DF-71A4F88F5080}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B09DE8E-9B7B-477F-86DF-71A4F88F5080}.Release|Any CPU.Build.0 = Release|Any CPU
		{B45E393C-C722-4AF1-B337-7E207D6F54E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B45E393C-C722-4AF1-B337-7E207D6F54E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B45E393C-C722-4AF1-B337-7E207D6F54E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B45E393C-C722-4AF1-B337-7E207D6F54E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C2130CC-7FDA-4EA3-B554-A0F9AF5FD719}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C2130CC-7FDA-4EA3-B554-A0F9AF5FD719}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C2130CC-7FDA-4EA3-B554-A0F9AF5FD719}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C2130CC-7FDA-4EA3-B554-A0F9AF5FD719}.Release|Any CPU.Build.0 = Release|Any CPU
		{A427250D-A012-46FE-B105-D9365FFD8321}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A427250D-A012-46FE-B105-D9365FFD8321}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A427250D-A012-46FE-B105-D9365FFD8321}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A427250D-A012-46FE-B105-D9365FFD8321}.Release|Any CPU.Build.0 = Release|Any CPU
		{42DB5BFA-D441-4518-9654-D405119FCE8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42DB5BFA-D441-4518-9654-D405119FCE8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42DB5BFA-D441-4518-9654-D405119FCE8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42DB5BFA-D441-4518-9654-D405119FCE8D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
