using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class ToolboxSessionType : ObjectType<ToolboxSession>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxSession> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.SessionTime).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.Topic).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Conductor).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.PhotoUrl).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Notes).Type<StringType>();
            descriptor.Field(t => t.Attendances).Type<ListType<ToolboxAttendanceType>>();
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<StringType>();
        }
    }

    public class ToolboxAttendanceType : ObjectType<ToolboxAttendance>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxAttendance> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.ToolboxSessionId).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Worker).Type<WorkerType>();
            descriptor.Field(t => t.WasPresent).Type<NonNullType<BooleanType>>();
            descriptor.Field(t => t.Notes).Type<StringType>();
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<StringType>();
        }
    }
} 