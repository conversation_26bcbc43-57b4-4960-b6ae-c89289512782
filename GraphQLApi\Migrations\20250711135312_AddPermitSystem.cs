﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class AddPermitSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Permits",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    JobId = table.Column<int>(type: "int", nullable: false),
                    PTWRefNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ProjectName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    StartingDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndingDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Location = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Hazards = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    PrecautionsRequired = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    PPE = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    DaysValid = table.Column<int>(type: "int", nullable: false),
                    PermitIssuer = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PermitReturn = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SignOff = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PermitType = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    WorkersHaveBeenTrained = table.Column<bool>(type: "bit", nullable: true),
                    NameOfTrainingOrganization = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    TopReading = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MidReading = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BottomReading = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EmergencyGuidelines = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    TaskObserver = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DepthOfExcavation = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ProtectionSystems = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ListOfEquipmentToBeUsed = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Inspections = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    InspectionAuthorization = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Isolation = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    WorkAreaInspectionAndPermitRenewal = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NatureOfWork = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FireExtinguishers = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FireSafetySupervisor = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ModeOfAccessToBeUsed = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    WorkAtHeightPermit_Inspections = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    WorkAtHeightPermit_InspectionAuthorization = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Permits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Permits_Jobs_JobId",
                        column: x => x.JobId,
                        principalTable: "Jobs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PermitDocuments",
                columns: table => new
                {
                    DocumentsId = table.Column<int>(type: "int", nullable: false),
                    PermitId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PermitDocuments", x => new { x.DocumentsId, x.PermitId });
                    table.ForeignKey(
                        name: "FK_PermitDocuments_DocumentFiles_DocumentsId",
                        column: x => x.DocumentsId,
                        principalTable: "DocumentFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PermitDocuments_Permits_PermitId",
                        column: x => x.PermitId,
                        principalTable: "Permits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PermitOtherPermits",
                columns: table => new
                {
                    OtherPermitId = table.Column<int>(type: "int", nullable: false),
                    PermitId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PermitOtherPermits", x => new { x.OtherPermitId, x.PermitId });
                    table.ForeignKey(
                        name: "FK_PermitOtherPermits_Permits_OtherPermitId",
                        column: x => x.OtherPermitId,
                        principalTable: "Permits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PermitOtherPermits_Permits_PermitId",
                        column: x => x.PermitId,
                        principalTable: "Permits",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_PermitDocuments_PermitId",
                table: "PermitDocuments",
                column: "PermitId");

            migrationBuilder.CreateIndex(
                name: "IX_PermitOtherPermits_PermitId",
                table: "PermitOtherPermits",
                column: "PermitId");

            migrationBuilder.CreateIndex(
                name: "IX_Permits_EndingDateTime",
                table: "Permits",
                column: "EndingDateTime");

            migrationBuilder.CreateIndex(
                name: "IX_Permits_JobId",
                table: "Permits",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_Permits_PTWRefNumber",
                table: "Permits",
                column: "PTWRefNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Permits_StartingDateTime",
                table: "Permits",
                column: "StartingDateTime");

            migrationBuilder.CreateIndex(
                name: "IX_Permits_Status",
                table: "Permits",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PermitDocuments");

            migrationBuilder.DropTable(
                name: "PermitOtherPermits");

            migrationBuilder.DropTable(
                name: "Permits");
        }
    }
}
