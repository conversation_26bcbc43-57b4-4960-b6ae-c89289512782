using HotChocolate.Types;
using HotChocolate;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace Shared.GraphQL.Types
{
    public class IncidentType : ObjectType<Incident>
    {
        protected override void Configure(IObjectTypeDescriptor<Incident> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Title).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Description).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.IncidentDate).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.Location).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Status).Type<NonNullType<EnumType<IncidentStatus>>>();

            // Optional fields
            descriptor.Field(t => t.ReportedBy).Type<StringType>();
            descriptor.Field(t => t.InvestigatedBy).Type<StringType>();
            descriptor.Field(t => t.Resolution).Type<StringType>();
            descriptor.Field(t => t.ResolvedDate).Type<DateTimeType>();

            // Navigation properties
            descriptor.Field(t => t.Workers).Type<ListType<WorkerType>>();

            // Audit fields
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
