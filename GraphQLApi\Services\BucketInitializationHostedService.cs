namespace GraphQLApi.Services
{
    /// <summary>
    /// Hosted service that initializes MinIO buckets at application startup
    /// </summary>
    public class BucketInitializationHostedService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<BucketInitializationHostedService> _logger;

        public BucketInitializationHostedService(
            IServiceProvider serviceProvider,
            ILogger<BucketInitializationHostedService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Starting bucket initialization hosted service...");

                using var scope = _serviceProvider.CreateScope();
                var bucketInitializationService = scope.ServiceProvider.GetRequiredService<IBucketInitializationService>();

                var success = await bucketInitializationService.InitializeAsync();

                if (success)
                {
                    _logger.LogInformation("Bucket initialization hosted service completed successfully");
                }
                else
                {
                    _logger.LogWarning("Bucket initialization hosted service completed with warnings");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bucket initialization hosted service");
                // Don't throw - we don't want to prevent the application from starting
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping bucket initialization hosted service...");
            return Task.CompletedTask;
        }
    }
}
