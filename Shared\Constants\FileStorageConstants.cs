namespace Shared.Constants
{
    /// <summary>
    /// Constants for file storage operations
    /// </summary>
    public static class FileStorageConstants
    {
        /// <summary>
        /// Maximum file size in bytes (50MB)
        /// </summary>
        public const long MAX_FILE_SIZE = 50 * 1024 * 1024;

        /// <summary>
        /// Default bucket names mapping
        /// </summary>
        public static class BucketNames
        {
            public const string PROFILE_PICTURE = "profile-picture";
            public const string CERTIFICATION = "certification";
            public const string SIGNATURES = "signatures";
            public const string TEMP = "temp";
            public const string DOCS = "docs";
        }

        /// <summary>
        /// Content type mappings for allowed file types
        /// </summary>
        public static class ContentTypes
        {
            // Images
            public const string JPEG = "image/jpeg";
            public const string JPG = "image/jpeg";
            public const string PNG = "image/png";
            public const string GIF = "image/gif";
            public const string BMP = "image/bmp";
            public const string WEBP = "image/webp";
            
            // Documents
            public const string PDF = "application/pdf";
            public const string TXT = "text/plain";
            public const string CSV = "text/csv";
            
            // Microsoft Office
            public const string DOC = "application/msword";
            public const string DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            public const string XLS = "application/vnd.ms-excel";
            public const string XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        }

        /// <summary>
        /// File extension mappings
        /// </summary>
        public static class FileExtensions
        {
            public static readonly Dictionary<string, string> ContentTypeMap = new()
            {
                { ".jpg", ContentTypes.JPG },
                { ".jpeg", ContentTypes.JPEG },
                { ".png", ContentTypes.PNG },
                { ".gif", ContentTypes.GIF },
                { ".bmp", ContentTypes.BMP },
                { ".webp", ContentTypes.WEBP },
                { ".pdf", ContentTypes.PDF },
                { ".txt", ContentTypes.TXT },
                { ".csv", ContentTypes.CSV },
                { ".doc", ContentTypes.DOC },
                { ".docx", ContentTypes.DOCX },
                { ".xls", ContentTypes.XLS },
                { ".xlsx", ContentTypes.XLSX }
            };

            public static readonly HashSet<string> AllowedExtensions = new()
            {
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
                ".pdf", ".txt", ".csv", ".doc", ".docx", ".xls", ".xlsx"
            };
        }

        /// <summary>
        /// Folder structure constants
        /// </summary>
        public static class FolderStructure
        {
            public const string YEAR_FORMAT = "yyyy";
            public const string MONTH_FORMAT = "MM";
            public const string DAY_FORMAT = "dd";
            public const string SEPARATOR = "/";
        }
    }
}
