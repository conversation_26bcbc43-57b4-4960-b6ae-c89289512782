﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class AddFileIntegrationToWorkerAndTraining : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ProfilePictureFileId",
                table: "Workers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SignatureFileId",
                table: "Workers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "DocumentFiles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FileMetadataId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    EntityType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    TrainingId = table.Column<int>(type: "int", nullable: true),
                    WorkerId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocumentFiles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DocumentFiles_FileMetadata_FileMetadataId",
                        column: x => x.FileMetadataId,
                        principalTable: "FileMetadata",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DocumentFiles_Trainings_TrainingId",
                        column: x => x.TrainingId,
                        principalTable: "Trainings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DocumentFiles_Workers_WorkerId",
                        column: x => x.WorkerId,
                        principalTable: "Workers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Workers_ProfilePictureFileId",
                table: "Workers",
                column: "ProfilePictureFileId");

            migrationBuilder.CreateIndex(
                name: "IX_Workers_SignatureFileId",
                table: "Workers",
                column: "SignatureFileId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_CreatedAt",
                table: "DocumentFiles",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_FileMetadataId",
                table: "DocumentFiles",
                column: "FileMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_IsDeleted",
                table: "DocumentFiles",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_Name",
                table: "DocumentFiles",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_Training",
                table: "DocumentFiles",
                columns: new[] { "EntityType", "TrainingId" },
                filter: "[EntityType] = 'Training' AND [TrainingId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_TrainingId",
                table: "DocumentFiles",
                column: "TrainingId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_Worker",
                table: "DocumentFiles",
                columns: new[] { "EntityType", "WorkerId" },
                filter: "[EntityType] = 'Worker' AND [WorkerId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_WorkerId",
                table: "DocumentFiles",
                column: "WorkerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Workers_FileMetadata_ProfilePictureFileId",
                table: "Workers",
                column: "ProfilePictureFileId",
                principalTable: "FileMetadata",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Workers_FileMetadata_SignatureFileId",
                table: "Workers",
                column: "SignatureFileId",
                principalTable: "FileMetadata",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Workers_FileMetadata_ProfilePictureFileId",
                table: "Workers");

            migrationBuilder.DropForeignKey(
                name: "FK_Workers_FileMetadata_SignatureFileId",
                table: "Workers");

            migrationBuilder.DropTable(
                name: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_Workers_ProfilePictureFileId",
                table: "Workers");

            migrationBuilder.DropIndex(
                name: "IX_Workers_SignatureFileId",
                table: "Workers");

            migrationBuilder.DropColumn(
                name: "ProfilePictureFileId",
                table: "Workers");

            migrationBuilder.DropColumn(
                name: "SignatureFileId",
                table: "Workers");
        }
    }
}
