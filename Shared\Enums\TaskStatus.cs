﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Enums
{
    public enum TaskStatus
    {
        TODO,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED,
        ON_HOLD
    }

    public enum TaskPriority
    {
        <PERSON><PERSON>,
        <PERSON><PERSON>UM,
        <PERSON>IGH,
        CRITIC<PERSON>
    }

    public enum InspectionStatus
    {
        NOT_REQUIRED,
        PENDING,
        IN_PROGRESS,
        COMPLETED,
        FAILED,
        REWORK_REQUIRED
    }

    public enum JobStatus
    {
        REQUESTED,
        BLOCKED,
        PENDING_APPROVAL,
        APPROVED,
        DISAPPROVED,
        FINISHED
    }

    public enum PermitType
    {
        GENERAL_WORK_PERMIT,
        HOT_WORK_PERMIT,
        CONFINED_SPACE_ENTRY_PERMIT,
        WORK_AT_HEIGHT_PERMIT,
        EXCAVATION_PERMIT
    }
}
