using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Task = Shared.GraphQL.Models.Task;

namespace GraphQLApi.Data.Configurations
{
    public class TaskConfiguration : IEntityTypeConfiguration<Task>
    {
        public void Configure(EntityTypeBuilder<Task> builder)
        {
            builder.HasKey(t => t.Id);

            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(t => t.Type)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.Description)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(t => t.TaskNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.HasIndex(t => t.TaskNumber)
                .IsUnique();

            builder.Property(t => t.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(t => t.Priority)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(t => t.InspectionStatus)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(t => t.TimeForCompletion)
                .HasMaxLength(100);

            builder.Property(t => t.Category)
                .HasMaxLength(100);

            builder.Property(t => t.AssociatedMethodStatement)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(t => t.ChiefEngineer)
                .WithMany()
                .HasForeignKey(t => t.ChiefEngineerId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasMany(t => t.WorkersAssigned)
                .WithMany()
                .UsingEntity(j => j.ToTable("TaskWorkers"));

            builder.HasMany(t => t.EquipmentInvolved)
                .WithMany(e => e.Tasks)
                .UsingEntity(j => j.ToTable("TaskEquipment"));

            // Audit fields
            builder.Property(t => t.CreatedAt)
                .IsRequired();

            builder.Property(t => t.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(t => t.DeletedBy)
                .HasMaxLength(100);
        }
    }
}
