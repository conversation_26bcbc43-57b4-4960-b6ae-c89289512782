using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class ToolboxSession : IAuditableEntity
    {
        public int Id { get; set; }
        public DateTime SessionTime { get; set; }
        public string Topic { get; set; }
        public string Conductor { get; set; }
        public string PhotoUrl { get; set; }
        public string? Notes { get; set; }
        public ICollection<ToolboxAttendance> Attendances { get; set; } = new List<ToolboxAttendance>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }

    public class ToolboxAttendance : IAuditableEntity
    {
        public int Id { get; set; }
        public int ToolboxSessionId { get; set; }
        public ToolboxSession ToolboxSession { get; set; }
        public int WorkerId { get; set; }
        public Worker Worker { get; set; }
        public bool WasPresent { get; set; }
        public string? Notes { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
} 