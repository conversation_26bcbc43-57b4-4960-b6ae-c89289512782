namespace GraphQLApi.GraphQL.Types
{
    /// <summary>
    /// Input type for hazard creation during job review
    /// </summary>
    public class HazardInput
    {
        /// <summary>
        /// Description of the hazard
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// List of control measures for this hazard
        /// </summary>
        public IEnumerable<string> ControlMeasures { get; set; } = new List<string>();
    }
}
