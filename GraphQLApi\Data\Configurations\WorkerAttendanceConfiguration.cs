using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class WorkerAttendanceConfiguration : IEntityTypeConfiguration<WorkerAttendance>
    {
        public void Configure(EntityTypeBuilder<WorkerAttendance> builder)
        {
            builder.ToTable("WorkerAttendances");

            builder.<PERSON><PERSON>ey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(e => e.Notes)
                .HasMaxLength(500);

            builder.HasOne(e => e.Worker)
                .WithMany()
                .HasForeignKey(e => e.WorkerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes for better query performance
            builder.HasIndex(e => e.WorkerId);
            builder.HasIndex(e => e.CheckInTime);
            builder.HasIndex(e => new { e.WorkerId, e.CheckInTime });
        }
    }
} 