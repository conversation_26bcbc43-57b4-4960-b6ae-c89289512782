using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public class TrainingStatusService : ITrainingStatusService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ILogger<TrainingStatusService> _logger;

        public TrainingStatusService(
            IDbContextFactory<AppDbContext> contextFactory,
            ILogger<TrainingStatusService> logger)
        {
            _contextFactory = contextFactory;
            _logger = logger;
        }

        public async System.Threading.Tasks.Task UpdateTrainingStatusesAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var now = DateTime.UtcNow;

            // Update training statuses based on dates
            var trainingsToUpdate = await context.Trainings
                .Where(t => !t.IsDeleted)
                .ToListAsync();

            foreach (var training in trainingsToUpdate)
            {
                var oldStatus = training.Status;
                var newStatus = CalculateTrainingStatus(training, now);

                if (oldStatus != newStatus)
                {
                    training.Status = newStatus;
                    _logger.LogInformation("Updated training {TrainingId} status from {OldStatus} to {NewStatus}",
                        training.Id, oldStatus, newStatus);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task UpdateTrainingHistoryStatusesAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var now = DateTime.UtcNow;

            // Update training history statuses based on expiry dates
            var historyToUpdate = await context.WorkerTrainingHistory
                .Where(h => h.Status == TrainingStatus.Completed && h.ExpiryDate.HasValue)
                .ToListAsync();

            foreach (var history in historyToUpdate)
            {
                if (history.ExpiryDate!.Value <= now && history.Status != TrainingStatus.Expired)
                {
                    history.Status = TrainingStatus.Expired;
                    _logger.LogInformation("Marked training history {HistoryId} as expired", history.Id);
                }
            }

            await context.SaveChangesAsync();
        }

        public async Task<WorkerTrainingHistory> CompleteTrainingAsync(int workerId, int trainingId, DateTime completionDate, decimal? score = null, string? notes = null)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Get the training to calculate expiry date
            var training = await context.Trainings.FindAsync(trainingId);
            if (training == null)
                throw new ArgumentException("Training not found", nameof(trainingId));

            // Calculate expiry date if training has validity period
            DateTime? expiryDate = null;
            if (training.ValidityPeriodMonths.HasValue)
            {
                expiryDate = completionDate.AddMonths(training.ValidityPeriodMonths.Value);
            }

            var history = new WorkerTrainingHistory
            {
                WorkerId = workerId,
                TrainingId = trainingId,
                CompletionDate = completionDate,
                ExpiryDate = expiryDate,
                Status = TrainingStatus.Completed,
                Score = score,
                Notes = notes
            };

            context.WorkerTrainingHistory.Add(history);
            await context.SaveChangesAsync();

            return history;
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetWorkerTrainingHistoryAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.WorkerTrainingHistory
                .Include(h => h.Training)
                .Where(h => h.WorkerId == workerId)
                .OrderByDescending(h => h.CompletionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiringTrainingsAsync(int daysAhead = 30)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var cutoffDate = DateTime.UtcNow.AddDays(daysAhead);

            return await context.WorkerTrainingHistory
                .Include(h => h.Worker)
                .Include(h => h.Training)
                .Where(h => h.Status == TrainingStatus.Completed &&
                           h.ExpiryDate.HasValue &&
                           h.ExpiryDate.Value <= cutoffDate &&
                           h.ExpiryDate.Value > DateTime.UtcNow)
                .OrderBy(h => h.ExpiryDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiredTrainingsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            return await context.WorkerTrainingHistory
                .Include(h => h.Worker)
                .Include(h => h.Training)
                .Where(h => h.ExpiryDate.HasValue && h.ExpiryDate.Value <= DateTime.UtcNow)
                .OrderBy(h => h.ExpiryDate)
                .ToListAsync();
        }

        private static TrainingStatus CalculateTrainingStatus(Training training, DateTime now)
        {
            // If training is cancelled or completed, don't change status
            if (training.Status == TrainingStatus.Cancelled || training.Status == TrainingStatus.Completed)
                return training.Status;

            // If training has start date
            if (training.StartDate.HasValue)
            {
                // If training hasn't started yet
                if (training.StartDate.Value > now)
                    return TrainingStatus.Scheduled;

                // If training has end date
                if (training.EndDate.HasValue)
                {
                    // If training is currently running
                    if (training.StartDate.Value <= now && training.EndDate.Value >= now)
                        return TrainingStatus.Active;

                    // If training has ended
                    if (training.EndDate.Value < now)
                        return TrainingStatus.Completed;
                }
                else
                {
                    // Training started but no end date specified, consider it active
                    return TrainingStatus.Active;
                }
            }

            // Default to current status if no dates are set
            return training.Status;
        }
    }
}
