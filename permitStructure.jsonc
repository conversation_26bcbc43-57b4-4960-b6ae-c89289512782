{
    "permit status enum": // desciption of the permit status
    "drafted | pending approval | opened | cancelled | pending closure | closed | voided | disapproved | expired ",
    // audit fileds and soft delete fileds are also added
    "base structure": {
        "jobId": "int", // foreign key to job table
        "PTWRefNumber": "string", // unique
        "projectName": "string",
        "startingDateTime": "datetime",
        "endingDateTime": "datetime",
        "description": "string",
        "location": "string",
        "hazards": "string",
        "precations Required": "string",
        "PPE": "string",
        "status": "permit status enum",
        "days valid": "int",
        // "other permits in use": many to many relationship with permits
        // "other documents attached": collections of documents
        "permit Issuer": { // json object of structure below
            "competentPersons": [ // is an array of objects of shape below
                {
                    "workerId": "int",
                    "name": "string",
                    "signatureFileId": "string",
                    "signedAt": "datetime"
                }
            ],
            "authorisedPersons": [ // is an array of objects of shape below
                {
                    "workerId": "int",
                    "name": "string",
                    "signatureFileId": "string",
                    "signedAt": "datetime"
                }
            ]
        },
        "permit Return": { // json object of structure below; nullable;
            "competentPersons": [ // is an array of objects of shape below
                {
                    "workerId": "int",
                    "name": "string",
                    "signatureFileId": "string",
                    "signedAt": "datetime"
                }
            ],
            "authorisedPersons": [ // is an array of objects of shape below
                {
                    "workerId": "int",
                    "name": "string",
                    "signatureFileId": "string",
                    "signedAt": "datetime"
                }
            ]
        },
        "signOff": { // json object of structure below;
            "dateTime": "datetime",
            "workers": [ // is an array of objects of shape below
                {
                    "workerId": "int",
                    "designation": "string",
                    "name": "string",
                    "signatureFileId": "string",
                    "signedAt": "datetime"
                }
            ]
        }
    },
    
    "General work permit": {
        "isolation": "string",
        "worke area inspection and permit renewal": // is a json array of objects of shape below
        {
            "name": "string",
            "signatureFileId": "string",
            "signedAt": "datetime",
            "comments": "string"
        }
    },

    "Excavation work permit": {
        "depth of excavation": "string",
        "protection systems": "string",
        "list of equipment to be used": "string",
        "inspections": "string",
        "inspectionAuthoization": // json of shape below
        {
            "nameOfInspector": "string",
            "designation": "string",
            "dateOfInspection": "datetime",
            "comments": "string"
        }
    },

    "Work at height permit": {
        "mode of access to be used": "string",
        "inspections": "string",
        "inspectionAuthoization": { // json of shape below
            "nameOfInspector": "string",
            "designation": "string",
            "dateOfInspection": "datetime",
            "comments": "string"
        }
    },

    "Confined space permit": {
        "workers have been trained": "bool",
        "name of training organization": "string",
        "top reading": { // json of shape below
            "oxygen": "string",
            "explosive": "string",
            "toxic": "string",
            "co2": "string"
        },
        "mid reading": { // json of shape below
            "oxygen": "string",
            "explosive": "string",
            "toxic": "string",
            "co2": "string"
        },
        "bottom reading": { // json of shape below
            "oxygen": "string",
            "explosive": "string",
            "toxic": "string",
            "co2": "string"
        },
        "emergency guidelines": "string",
        "task observer": { // json of shape below
            "workerId": "int",
            "name": "string",
            "signatureFileId": "string",
            "signedAt": "datetime"
        }
    },

    "Hot work permit": {
        "nature of work": "string",
        "fire extinguishers": "string",
        "fire safety supervisor": { // json of shape below
            "workerId": "int",
            "name": "string",
            "signatureFileId": "string",
            "signedAt": "datetime"
        }
    }
}