using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class CategoryType : ObjectType<Category>
    {
        protected override void Configure(IObjectTypeDescriptor<Category> descriptor)
        {
            descriptor.Field(c => c.Id).Type<NonNullType<IntType>>();
            descriptor.Field(c => c.Description).Type<NonNullType<StringType>>();

            // Jobs using this category
            descriptor.Field(c => c.Jobs).Type<ListType<JobType>>();

            // Audit fields
            descriptor.Field(c => c.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(c => c.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(c => c.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(c => c.UpdatedBy).Type<StringType>();
        }
    }
}
