using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class GeneralWorkPermitConfiguration : IEntityTypeConfiguration<GeneralWorkPermit>
    {
        public void Configure(EntityTypeBuilder<GeneralWorkPermit> builder)
        {
            builder.Property(p => p.Isolation)
                .HasMaxLength(1000);

            builder.Property(p => p.WorkAreaInspectionAndPermitRenewal)
                .IsRequired()
                .HasColumnType("nvarchar(max)");
        }
    }
}
