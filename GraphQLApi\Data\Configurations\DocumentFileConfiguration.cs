using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    /// <summary>
    /// Entity configuration for DocumentFile
    /// </summary>
    public class DocumentFileConfiguration : IEntityTypeConfiguration<DocumentFile>
    {
        public void Configure(EntityTypeBuilder<DocumentFile> builder)
        {
            builder.ToTable("DocumentFiles");

            // Primary key
            builder.HasKey(d => d.Id);
            builder.Property(d => d.Id)
                .UseIdentityColumn();

            // Required properties
            builder.Property(d => d.Name)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(d => d.FileMetadataId)
                .IsRequired();

            // Foreign key relationship
            builder.HasOne(d => d.FileMetadata)
                .WithMany()
                .HasForeignKey(d => d.FileMetadataId)
                .OnDelete(DeleteBehavior.NoAction);

            // Audit fields
            builder.Property(d => d.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(d => d.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(d => d.UpdatedAt)
                .HasColumnType("datetime2");

            builder.Property(d => d.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(d => d.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(d => d.DeletedAt)
                .HasColumnType("datetime2");

            builder.Property(d => d.DeletedBy)
                .HasMaxLength(100);

            // Indexes for performance
            builder.HasIndex(d => d.FileMetadataId)
                .HasDatabaseName("IX_DocumentFiles_FileMetadataId");

            builder.HasIndex(d => d.Name)
                .HasDatabaseName("IX_DocumentFiles_Name");

            builder.HasIndex(d => d.IsDeleted)
                .HasDatabaseName("IX_DocumentFiles_IsDeleted");

            builder.HasIndex(d => d.CreatedAt)
                .HasDatabaseName("IX_DocumentFiles_CreatedAt");

            // Add discriminator for polymorphic relationships
            builder.Property<string>("EntityType")
                .HasMaxLength(50);

            builder.Property<int?>("WorkerId");
            builder.Property<int?>("TrainingId");

            builder.HasIndex("EntityType", "WorkerId")
                .HasFilter("[EntityType] = 'Worker' AND [WorkerId] IS NOT NULL")
                .HasDatabaseName("IX_DocumentFiles_Worker");

            builder.HasIndex("EntityType", "TrainingId")
                .HasFilter("[EntityType] = 'Training' AND [TrainingId] IS NOT NULL")
                .HasDatabaseName("IX_DocumentFiles_Training");
        }
    }
}
