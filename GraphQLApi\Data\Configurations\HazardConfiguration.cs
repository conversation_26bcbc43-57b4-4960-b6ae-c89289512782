using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class HazardConfiguration : IEntityTypeConfiguration<Hazard>
    {
        public void Configure(EntityTypeBuilder<Hazard> builder)
        {
            builder.HasKey(h => h.Id);

            builder.Property(h => h.Description)
                .IsRequired()
                .HasMaxLength(1000);

            // Configure relationship with Job
            builder.HasOne(h => h.Job)
                .WithMany(j => j.Hazards)
                .HasForeignKey(h => h.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure relationship with ControlMeasures
            builder.HasMany(h => h.ControlMeasures)
                .WithOne(cm => cm.Hazard)
                .HasForeignKey(cm => cm.HazardId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit fields
            builder.Property(h => h.CreatedAt)
                .IsRequired();

            builder.Property(h => h.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(h => h.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(h => h.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(h => h.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(h => h.JobId);
        }
    }
}
